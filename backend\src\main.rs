mod config;
mod database;
mod scanner;
mod handlers;
mod models;
mod error;

use axum::{
    routing::{get, post},
    Router,
};
use std::sync::Arc;
use tower_http::{
    cors::CorsLayer,
    services::ServeDir,
    trace::TraceLayer,
};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

use crate::{
    config::Config,
    database::Database,
    handlers::{
        get_folders, get_photos, get_photo_file, rescan_photos, health_check, get_scan_status
    },
    scanner::PhotoScanner,
};

#[derive(Clone)]
pub struct AppState {
    pub db: Arc<Database>,
    pub scanner: Arc<PhotoScanner>,
    pub config: Arc<Config>,
}

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // 初始化日志
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "picsss_backend=debug,tower_http=debug".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    // 加载配置
    let config = Arc::new(Config::from_env()?);
    tracing::info!("配置加载完成: {:?}", config);

    // 初始化数据库
    let db = Arc::new(Database::new(&config.database_path).await?);
    tracing::info!("数据库初始化完成");

    // 初始化扫描器
    let scanner = Arc::new(PhotoScanner::new(
        config.photos_root_path.clone(),
        config.cache_path.clone(),
        db.clone(),
    ));
    tracing::info!("扫描器初始化完成");

    // 创建应用状态
    let state = AppState {
        db,
        scanner: scanner.clone(),
        config: config.clone(),
    };

    // 启动后台扫描任务
    let scanner_clone = scanner.clone();
    tokio::spawn(async move {
        if let Err(e) = scanner_clone.initial_scan().await {
            tracing::error!("初始扫描失败: {}", e);
        }
    });

    // 构建路由
    let app = Router::new()
        .route("/health", get(health_check))
        .route("/api/folders", get(get_folders))
        .route("/api/photos", get(get_photos))
        .route("/api/rescan", post(rescan_photos))
        .route("/api/scan-status", get(get_scan_status))
        .route("/photo/*path", get(get_photo_file))
        .nest_service("/", ServeDir::new(&config.static_path))
        .layer(CorsLayer::permissive())
        .layer(TraceLayer::new_for_http())
        .with_state(state);

    // 启动服务器
    let listener = tokio::net::TcpListener::bind(format!("0.0.0.0:{}", config.server_port)).await?;
    tracing::info!("服务器启动在端口 {}", config.server_port);
    
    axum::serve(listener, app).await?;

    Ok(())
}
