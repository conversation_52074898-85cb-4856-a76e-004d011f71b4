# 多阶段构建 Dockerfile

# 阶段1: 构建前端
FROM node:18-alpine AS frontend-builder

WORKDIR /app/frontend

# 复制前端依赖文件
COPY frontend/package*.json ./

# 安装前端依赖
RUN npm install

# 复制前端源码
COPY frontend/ ./

# 构建前端
RUN npm run build

# 阶段2: 构建后端
FROM rust:1.75-alpine AS backend-builder

# 安装构建依赖
RUN apk add --no-cache musl-dev pkgconfig openssl-dev

WORKDIR /app/backend

# 复制后端依赖文件
COPY backend/Cargo.toml backend/Cargo.lock ./

# 创建虚拟源文件以缓存依赖
RUN mkdir src && echo "fn main() {}" > src/main.rs

# 构建依赖
RUN cargo build --release

# 删除虚拟源文件
RUN rm -rf src

# 复制真实源码
COPY backend/src ./src

# 重新构建应用
RUN touch src/main.rs && cargo build --release

# 阶段3: 运行时镜像
FROM alpine:latest

# 安装运行时依赖
RUN apk add --no-cache ca-certificates

WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=backend-builder /app/backend/target/release/picsss-backend ./picsss-backend

# 从前端构建阶段复制静态文件
COPY --from=frontend-builder /app/frontend/build ./static

# 创建必要的目录
RUN mkdir -p /app/data/.cache/thumbnails

# 设置权限
RUN chmod +x ./picsss-backend

# 暴露端口
EXPOSE 3000

# 启动应用
CMD ["./picsss-backend"]
