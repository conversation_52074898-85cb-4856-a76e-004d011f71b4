services:
  # 后端开发服务
  backend-dev:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      - "./backend:/app"
      - "./photos:/app/photos:ro"
      - "./data:/app/data"
    environment:
      - PHOTOS_ROOT_PATH=/app/photos
      - SERVER_PORT=3000
      - DATABASE_PATH=/app/data/database.db
      - CACHE_PATH=/app/data/.cache
      - STATIC_PATH=/app/static
      - RUST_LOG=debug
    working_dir: /app
    command: cargo watch -x run

  # 前端开发服务
  frontend-dev:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    ports:
      - "5173:5173"
    volumes:
      - "./frontend:/app"
      - "/app/node_modules"
    environment:
      - NODE_ENV=development
    working_dir: /app
    command: npm run dev -- --host 0.0.0.0

networks:
  default:
    driver: bridge
