<script>
  import { onMount, onD<PERSON>roy } from 'svelte';
  import { scanStatus } from '../stores.js';

  let intervalId;

  async function fetchScanStatus() {
    try {
      const response = await fetch('/api/scan-status');
      const result = await response.json();
      
      if (result.success) {
        scanStatus.set(result.data);
      }
    } catch (error) {
      console.error('获取扫描状态失败:', error);
    }
  }

  onMount(() => {
    fetchScanStatus();
    
    // 每2秒更新一次扫描状态
    intervalId = setInterval(fetchScanStatus, 2000);
  });

  onDestroy(() => {
    if (intervalId) {
      clearInterval(intervalId);
    }
  });

  $: progress = $scanStatus.total_photos > 0 
    ? ($scanStatus.processed_photos / $scanStatus.total_photos) * 100 
    : 0;
</script>

{#if $scanStatus.is_scanning}
  <div class="scan-status">
    <div class="scan-header">
      <span class="scan-title">正在扫描图片...</span>
      <span class="scan-progress">
        {$scanStatus.processed_photos} / {$scanStatus.total_photos}
      </span>
    </div>
    
    <div class="progress-bar">
      <div class="progress-fill" style="width: {progress}%"></div>
    </div>
    
    {#if $scanStatus.current_folder}
      <div class="current-folder">
        当前文件夹: {$scanStatus.current_folder}
      </div>
    {/if}
  </div>
{/if}

<style>
  .scan-status {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 1rem;
    border-radius: 8px;
    min-width: 300px;
    z-index: 999;
    border: 1px solid #444;
  }

  .scan-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
  }

  .scan-title {
    font-weight: 500;
  }

  .scan-progress {
    font-size: 0.9rem;
    color: #ccc;
  }

  .progress-bar {
    width: 100%;
    height: 6px;
    background-color: #333;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 0.5rem;
  }

  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007acc, #00a8ff);
    transition: width 0.3s ease;
  }

  .current-folder {
    font-size: 0.8rem;
    color: #aaa;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
</style>
