use crate::{
    database::Database,
    error::{AppError, Result},
    models::Photo,
};
use image::{imageops::FilterType, DynamicImage, ImageFormat};
use std::{
    collections::HashSet,
    path::{Path, PathBuf},
    sync::{
        atomic::{AtomicBool, AtomicI64, Ordering},
        Arc,
    },
};
use tokio::sync::RwLock;
use walkdir::WalkDir;

pub struct PhotoScanner {
    root_path: PathBuf,
    cache_path: PathBuf,
    db: Arc<Database>,
    is_scanning: Arc<AtomicBool>,
    total_photos: Arc<AtomicI64>,
    processed_photos: Arc<AtomicI64>,
    current_folder: Arc<RwLock<Option<String>>>,
}

impl PhotoScanner {
    pub fn new(root_path: PathBuf, cache_path: PathBuf, db: Arc<Database>) -> Self {
        Self {
            root_path,
            cache_path,
            db,
            is_scanning: Arc::new(AtomicBool::new(false)),
            total_photos: Arc::new(AtomicI64::new(0)),
            processed_photos: Arc::new(AtomicI64::new(0)),
            current_folder: Arc::new(RwLock::new(None)),
        }
    }

    pub async fn initial_scan(&self) -> Result<()> {
        tracing::info!("开始初始扫描...");
        self.scan_directories().await
    }

    pub async fn rescan(&self) -> Result<()> {
        if self.is_scanning.load(Ordering::Relaxed) {
            return Err(AppError::Internal("扫描正在进行中".to_string()));
        }

        tracing::info!("开始重新扫描...");
        
        // 清空数据库
        self.db.clear_all_photos().await?;
        
        // 重新扫描
        self.scan_directories().await
    }

    pub async fn scan_directories(&self) -> Result<()> {
        self.is_scanning.store(true, Ordering::Relaxed);
        self.processed_photos.store(0, Ordering::Relaxed);

        // 确保缓存目录存在
        let thumbnails_dir = self.cache_path.join("thumbnails");
        tokio::fs::create_dir_all(&thumbnails_dir).await?;

        // 支持的图片格式
        let supported_extensions: HashSet<&str> = 
            ["jpg", "jpeg", "png", "gif", "webp", "bmp", "tiff", "tif"]
            .iter().cloned().collect();

        // 收集所有图片文件
        let mut image_files = Vec::new();
        
        for entry in WalkDir::new(&self.root_path)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_file() {
                if let Some(extension) = entry.path().extension() {
                    if let Some(ext_str) = extension.to_str() {
                        if supported_extensions.contains(ext_str.to_lowercase().as_str()) {
                            image_files.push(entry.path().to_path_buf());
                        }
                    }
                }
            }
        }

        self.total_photos.store(image_files.len() as i64, Ordering::Relaxed);
        tracing::info!("找到 {} 个图片文件", image_files.len());

        // 处理每个图片文件
        for (index, file_path) in image_files.iter().enumerate() {
            if let Some(folder_path) = file_path.parent() {
                let mut current_folder = self.current_folder.write().await;
                *current_folder = Some(folder_path.to_string_lossy().to_string());
            }

            if let Err(e) = self.process_image_file(file_path).await {
                tracing::warn!("处理图片文件失败 {}: {}", file_path.display(), e);
            }

            self.processed_photos.store((index + 1) as i64, Ordering::Relaxed);

            // 每处理100个文件输出一次进度
            if (index + 1) % 100 == 0 {
                tracing::info!("已处理 {}/{} 个文件", index + 1, image_files.len());
            }
        }

        self.is_scanning.store(false, Ordering::Relaxed);
        let mut current_folder = self.current_folder.write().await;
        *current_folder = None;

        tracing::info!("扫描完成，共处理 {} 个图片文件", image_files.len());
        Ok(())
    }

    async fn process_image_file(&self, file_path: &Path) -> Result<()> {
        // 检查文件是否已存在于数据库中
        let file_path_str = file_path.to_string_lossy().to_string();
        if self.db.photo_exists(&file_path_str).await? {
            return Ok(());
        }

        // 获取文件信息
        let metadata = tokio::fs::metadata(file_path).await?;
        let file_size = metadata.len();
        let file_name = file_path
            .file_name()
            .unwrap_or_default()
            .to_string_lossy()
            .to_string();

        // 生成缩略图路径
        let thumbnail_filename = format!("{}.webp", uuid::Uuid::new_v4());
        let thumbnail_path = self.cache_path.join("thumbnails").join(&thumbnail_filename);

        // 在后台线程中处理图片（避免阻塞异步运行时）
        let file_path_clone = file_path.to_path_buf();
        let thumbnail_path_clone = thumbnail_path.clone();
        
        let (width, height) = tokio::task::spawn_blocking(move || -> Result<(u32, u32)> {
            Self::generate_thumbnail(&file_path_clone, &thumbnail_path_clone)
        }).await.map_err(|e| AppError::Internal(format!("任务执行失败: {}", e)))??;

        // 创建Photo对象
        let folder_path = file_path.parent().unwrap_or(Path::new("")).to_path_buf();
        let photo = Photo::new(
            file_path.to_path_buf(),
            folder_path,
            thumbnail_path,
            file_name,
            file_size,
            width,
            height,
        );

        // 保存到数据库
        self.db.insert_photo(&photo).await?;

        Ok(())
    }

    fn generate_thumbnail(file_path: &Path, thumbnail_path: &Path) -> Result<(u32, u32)> {
        // 打开图片
        let img = image::open(file_path)?;
        let (original_width, original_height) = img.dimensions();

        // 生成缩略图（固定高度400px，保持宽高比）
        const THUMBNAIL_HEIGHT: u32 = 400;
        let aspect_ratio = original_width as f32 / original_height as f32;
        let thumbnail_width = (THUMBNAIL_HEIGHT as f32 * aspect_ratio) as u32;

        let thumbnail = img.resize(thumbnail_width, THUMBNAIL_HEIGHT, FilterType::Lanczos3);

        // 保存为WebP格式
        thumbnail.save_with_format(thumbnail_path, ImageFormat::WebP)?;

        Ok((original_width, original_height))
    }

    pub fn get_scan_status(&self) -> crate::models::ScanStatus {
        let current_folder = self.current_folder.try_read()
            .map(|guard| guard.clone())
            .unwrap_or(None);

        crate::models::ScanStatus {
            is_scanning: self.is_scanning.load(Ordering::Relaxed),
            total_photos: self.total_photos.load(Ordering::Relaxed),
            processed_photos: self.processed_photos.load(Ordering::Relaxed),
            current_folder,
        }
    }
}
