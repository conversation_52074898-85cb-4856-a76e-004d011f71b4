<script>
  import { onMount } from 'svelte';
  import Sidebar from '../lib/components/Sidebar.svelte';
  import PhotoGrid from '../lib/components/PhotoGrid.svelte';
  import PhotoViewer from '../lib/components/PhotoViewer.svelte';
  import ScanStatus from '../lib/components/ScanStatus.svelte';
  import { selectedFolder, currentPhotos, viewerPhoto, isViewerOpen } from '../lib/stores.js';

  let sidebarCollapsed = false;
  let isLoading = false;

  function toggleSidebar() {
    sidebarCollapsed = !sidebarCollapsed;
  }

  async function handleRescan() {
    if (isLoading) return;
    
    isLoading = true;
    try {
      const response = await fetch('/api/rescan', {
        method: 'POST'
      });
      
      if (response.ok) {
        // 重新加载文件夹列表
        window.location.reload();
      } else {
        console.error('重新扫描失败');
      }
    } catch (error) {
      console.error('重新扫描错误:', error);
    } finally {
      isLoading = false;
    }
  }

  // 键盘事件处理
  function handleKeydown(event) {
    if ($isViewerOpen) {
      if (event.key === 'Escape') {
        isViewerOpen.set(false);
      } else if (event.key === 'ArrowLeft') {
        // 上一张图片
        const currentIndex = $currentPhotos.findIndex(p => p.id === $viewerPhoto?.id);
        if (currentIndex > 0) {
          viewerPhoto.set($currentPhotos[currentIndex - 1]);
        }
      } else if (event.key === 'ArrowRight') {
        // 下一张图片
        const currentIndex = $currentPhotos.findIndex(p => p.id === $viewerPhoto?.id);
        if (currentIndex < $currentPhotos.length - 1) {
          viewerPhoto.set($currentPhotos[currentIndex + 1]);
        }
      }
    }
  }

  onMount(() => {
    // 从localStorage恢复上次选择的文件夹
    const lastFolder = localStorage.getItem('lastSelectedFolder');
    if (lastFolder) {
      selectedFolder.set(lastFolder);
    }
  });
</script>

<svelte:window on:keydown={handleKeydown} />

<div class="app-container">
  <div class="sidebar" class:collapsed={sidebarCollapsed}>
    <div class="sidebar-header">
      <h1 class="sidebar-title">写真集浏览器</h1>
      <button class="toggle-btn" on:click={toggleSidebar}>
        {sidebarCollapsed ? '→' : '←'}
      </button>
    </div>
    <div class="sidebar-content">
      <Sidebar />
    </div>
  </div>

  <div class="main-content">
    <div class="main-header">
      <div>
        {#if $selectedFolder}
          <h2>当前文件夹: {$selectedFolder}</h2>
        {:else}
          <h2>请选择一个文件夹</h2>
        {/if}
      </div>
      <div>
        <button 
          class="btn btn-primary" 
          on:click={handleRescan}
          disabled={isLoading}
        >
          {#if isLoading}
            <span class="spinner"></span>
          {/if}
          重新扫描
        </button>
      </div>
    </div>
    
    <div class="main-body">
      <PhotoGrid />
    </div>
  </div>

  {#if $isViewerOpen}
    <PhotoViewer />
  {/if}

  <!-- 扫描状态显示 -->
  <ScanStatus />
</div>
