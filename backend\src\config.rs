use std::env;
use std::path::Path<PERSON>uf;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct Config {
    pub photos_root_path: <PERSON><PERSON><PERSON>,
    pub server_port: u16,
    pub database_path: <PERSON><PERSON>uf,
    pub cache_path: <PERSON><PERSON>uf,
    pub static_path: <PERSON><PERSON>uf,
}

impl Config {
    pub fn from_env() -> anyhow::Result<Self> {
        // 加载 .env 文件（如果存在）
        dotenvy::dotenv().ok();

        let photos_root_path = env::var("PHOTOS_ROOT_PATH")
            .unwrap_or_else(|_| "./photos".to_string())
            .into();

        let server_port = env::var("SERVER_PORT")
            .unwrap_or_else(|_| "3000".to_string())
            .parse::<u16>()?;

        let database_path = env::var("DATABASE_PATH")
            .unwrap_or_else(|_| "./data/database.db".to_string())
            .into();

        let cache_path = env::var("CACHE_PATH")
            .unwrap_or_else(|_| "./data/.cache".to_string())
            .into();

        let static_path = env::var("STATIC_PATH")
            .unwrap_or_else(|_| "./static".to_string())
            .into();

        Ok(Self {
            photos_root_path,
            server_port,
            database_path,
            cache_path,
            static_path,
        })
    }
}
