#!/bin/bash

# 测试构建脚本 - 验证项目是否可以正确构建

set -e

echo "🧪 开始测试构建..."

# 检查必要文件是否存在
echo "📋 检查项目文件..."

required_files=(
    "Dockerfile"
    "docker-compose.yml"
    "backend/Cargo.toml"
    "backend/src/main.rs"
    "frontend/package.json"
    "frontend/src/routes/+page.svelte"
)

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 缺少必要文件: $file"
        exit 1
    fi
done

echo "✅ 所有必要文件都存在"

# 创建测试目录
echo "📁 创建测试目录..."
mkdir -p photos data/.cache/thumbnails

# 测试后端构建
echo "🦀 测试后端构建..."
cd backend

# 检查 Rust 语法
if command -v cargo &> /dev/null; then
    echo "检查 Rust 代码语法..."
    cargo check
    echo "✅ 后端代码语法检查通过"
else
    echo "⚠️  Cargo 未安装，跳过后端语法检查"
fi

cd ..

# 测试前端构建
echo "🌐 测试前端构建..."
cd frontend

# 检查 Node.js 依赖
if command -v npm &> /dev/null; then
    echo "安装前端依赖..."
    npm ci
    
    echo "检查前端代码..."
    npm run check
    
    echo "构建前端..."
    npm run build
    
    echo "✅ 前端构建成功"
else
    echo "⚠️  npm 未安装，跳过前端构建测试"
fi

cd ..

# 测试 Docker 构建
echo "🐳 测试 Docker 构建..."
if command -v docker &> /dev/null; then
    echo "构建 Docker 镜像..."
    docker build -t picsss-test .
    
    echo "✅ Docker 镜像构建成功"
    
    # 清理测试镜像
    docker rmi picsss-test
else
    echo "⚠️  Docker 未安装，跳过 Docker 构建测试"
fi

echo "🎉 所有测试通过！项目可以正常构建。"
