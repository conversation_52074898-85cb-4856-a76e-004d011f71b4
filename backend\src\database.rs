use crate::{error::Result, models::Photo};
use sqlx::{sqlite::SqlitePool, Row};
use std::path::Path;

pub struct Database {
    pool: SqlitePool,
}

impl Database {
    pub async fn new(database_path: &Path) -> Result<Self> {
        // 确保数据库目录存在
        if let Some(parent) = database_path.parent() {
            tokio::fs::create_dir_all(parent).await?;
        }

        let database_url = format!("sqlite:{}", database_path.display());
        let pool = SqlitePool::connect(&database_url).await?;

        let db = Self { pool };
        db.migrate().await?;

        Ok(db)
    }

    async fn migrate(&self) -> Result<()> {
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS photos (
                id TEXT PRIMARY KEY,
                file_path TEXT NOT NULL UNIQUE,
                folder_path TEXT NOT NULL,
                thumbnail_path TEXT NOT NULL,
                file_name TEXT NOT NULL,
                file_size INTEGER NOT NULL,
                width INTEGER NOT NULL,
                height INTEGER NOT NULL,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL
            );

            CREATE INDEX IF NOT EXISTS idx_photos_folder_path ON photos(folder_path);
            CREATE INDEX IF NOT EXISTS idx_photos_file_name ON photos(file_name);
            CREATE INDEX IF NOT EXISTS idx_photos_created_at ON photos(created_at);
            "#,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn insert_photo(&self, photo: &Photo) -> Result<()> {
        sqlx::query(
            r#"
            INSERT OR REPLACE INTO photos 
            (id, file_path, folder_path, thumbnail_path, file_name, file_size, width, height, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#,
        )
        .bind(&photo.id)
        .bind(&photo.file_path)
        .bind(&photo.folder_path)
        .bind(&photo.thumbnail_path)
        .bind(&photo.file_name)
        .bind(photo.file_size)
        .bind(photo.width)
        .bind(photo.height)
        .bind(&photo.created_at)
        .bind(&photo.updated_at)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_photos_by_folder(&self, folder_path: &str) -> Result<Vec<Photo>> {
        let photos = sqlx::query_as::<_, Photo>(
            "SELECT * FROM photos WHERE folder_path = ? ORDER BY file_name ASC",
        )
        .bind(folder_path)
        .fetch_all(&self.pool)
        .await?;

        Ok(photos)
    }

    pub async fn get_all_folders(&self) -> Result<Vec<String>> {
        let rows = sqlx::query("SELECT DISTINCT folder_path FROM photos ORDER BY folder_path ASC")
            .fetch_all(&self.pool)
            .await?;

        let folders = rows
            .into_iter()
            .map(|row| row.get::<String, _>("folder_path"))
            .collect();

        Ok(folders)
    }

    pub async fn get_folder_cover(&self, folder_path: &str) -> Result<Option<String>> {
        let row = sqlx::query(
            "SELECT thumbnail_path FROM photos WHERE folder_path = ? ORDER BY file_name ASC LIMIT 1",
        )
        .bind(folder_path)
        .fetch_optional(&self.pool)
        .await?;

        Ok(row.map(|r| r.get::<String, _>("thumbnail_path")))
    }

    pub async fn get_folder_photo_count(&self, folder_path: &str) -> Result<i64> {
        let row = sqlx::query("SELECT COUNT(*) as count FROM photos WHERE folder_path = ?")
            .bind(folder_path)
            .fetch_one(&self.pool)
            .await?;

        Ok(row.get::<i64, _>("count"))
    }

    pub async fn clear_all_photos(&self) -> Result<()> {
        sqlx::query("DELETE FROM photos")
            .execute(&self.pool)
            .await?;

        Ok(())
    }

    pub async fn photo_exists(&self, file_path: &str) -> Result<bool> {
        let row = sqlx::query("SELECT COUNT(*) as count FROM photos WHERE file_path = ?")
            .bind(file_path)
            .fetch_one(&self.pool)
            .await?;

        Ok(row.get::<i64, _>("count") > 0)
    }

    pub async fn get_total_photo_count(&self) -> Result<i64> {
        let row = sqlx::query("SELECT COUNT(*) as count FROM photos")
            .fetch_one(&self.pool)
            .await?;

        Ok(row.get::<i64, _>("count"))
    }
}
