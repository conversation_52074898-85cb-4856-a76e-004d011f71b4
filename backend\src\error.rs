use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use serde_json::json;
use thiserror::Error;

#[derive(Error, Debug)]
pub enum AppError {
    #[error("数据库错误: {0}")]
    Database(#[from] sqlx::Error),
    
    #[error("IO错误: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("图像处理错误: {0}")]
    Image(#[from] image::ImageError),
    
    #[error("序列化错误: {0}")]
    Serialization(#[from] serde_json::Error),
    
    #[error("Base64解码错误: {0}")]
    Base64(#[from] base64::DecodeError),
    
    #[error("路径不存在: {0}")]
    PathNotFound(String),
    
    #[error("无效的文件格式")]
    InvalidFileFormat,
    
    #[error("内部服务器错误: {0}")]
    Internal(String),
}

impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        let (status, error_message) = match self {
            AppError::Database(_) => (StatusCode::INTERNAL_SERVER_ERROR, "数据库错误"),
            AppError::Io(_) => (StatusCode::INTERNAL_SERVER_ERROR, "文件系统错误"),
            AppError::Image(_) => (StatusCode::BAD_REQUEST, "图像处理错误"),
            AppError::Serialization(_) => (StatusCode::BAD_REQUEST, "数据格式错误"),
            AppError::Base64(_) => (StatusCode::BAD_REQUEST, "编码错误"),
            AppError::PathNotFound(_) => (StatusCode::NOT_FOUND, "文件未找到"),
            AppError::InvalidFileFormat => (StatusCode::BAD_REQUEST, "不支持的文件格式"),
            AppError::Internal(_) => (StatusCode::INTERNAL_SERVER_ERROR, "内部服务器错误"),
        };

        let body = Json(json!({
            "error": error_message,
            "details": self.to_string()
        }));

        (status, body).into_response()
    }
}

pub type Result<T> = std::result::Result<T, AppError>;
