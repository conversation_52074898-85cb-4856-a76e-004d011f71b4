use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use std::path::PathBuf;
use uuid::Uuid;
use base64::{engine::general_purpose, Engine as _};

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Photo {
    pub id: String,
    pub file_path: String,
    pub folder_path: String,
    pub thumbnail_path: String,
    pub file_name: String,
    pub file_size: i64,
    pub width: i32,
    pub height: i32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl Photo {
    pub fn new(
        file_path: PathBuf,
        folder_path: PathBuf,
        thumbnail_path: PathBuf,
        file_name: String,
        file_size: u64,
        width: u32,
        height: u32,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4().to_string(),
            file_path: file_path.to_string_lossy().to_string(),
            folder_path: folder_path.to_string_lossy().to_string(),
            thumbnail_path: thumbnail_path.to_string_lossy().to_string(),
            file_name,
            file_size: file_size as i64,
            width: width as i32,
            height: height as i32,
            created_at: now,
            updated_at: now,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PhotoResponse {
    pub id: String,
    pub original_path: String,
    pub thumbnail_path: String,
    pub file_name: String,
    pub width: i32,
    pub height: i32,
}

impl From<Photo> for PhotoResponse {
    fn from(photo: Photo) -> Self {
        Self {
            id: photo.id,
            original_path: base64::engine::general_purpose::STANDARD.encode(&photo.file_path),
            thumbnail_path: photo.thumbnail_path,
            file_name: photo.file_name,
            width: photo.width,
            height: photo.height,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FolderInfo {
    pub path: String,
    pub name: String,
    pub cover_thumbnail: Option<String>,
    pub photo_count: i64,
    pub children: Vec<FolderInfo>,
}

impl FolderInfo {
    pub fn new(path: String, name: String) -> Self {
        Self {
            path,
            name,
            cover_thumbnail: None,
            photo_count: 0,
            children: Vec::new(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScanStatus {
    pub is_scanning: bool,
    pub total_photos: i64,
    pub processed_photos: i64,
    pub current_folder: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub message: Option<String>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: None,
        }
    }

    pub fn error(message: String) -> Self {
        Self {
            success: false,
            data: None,
            message: Some(message),
        }
    }
}
