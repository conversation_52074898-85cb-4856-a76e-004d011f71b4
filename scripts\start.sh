#!/bin/bash

# 启动脚本 - 用于快速部署和启动应用

set -e

echo "🚀 启动本地写真集浏览器..."

# 检查是否存在 docker-compose
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose 未安装，请先安装 Docker 和 docker-compose"
    exit 1
fi

# 检查是否存在 photos 目录
if [ ! -d "./photos" ]; then
    echo "📁 创建示例 photos 目录..."
    mkdir -p ./photos
    echo "请将您的图片文件放入 ./photos 目录中"
fi

# 检查是否存在 data 目录
if [ ! -d "./data" ]; then
    echo "📁 创建 data 目录..."
    mkdir -p ./data/.cache/thumbnails
fi

# 复制环境配置文件
if [ ! -f ".env" ]; then
    echo "📝 创建环境配置文件..."
    cp .env.example .env
fi

echo "🔨 构建并启动容器..."
docker-compose up --build

echo "✅ 应用已启动！"
echo "🌐 请访问: http://localhost:3000"
