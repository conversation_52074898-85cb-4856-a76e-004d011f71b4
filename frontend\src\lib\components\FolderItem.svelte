<script>
  import { createEventDispatcher } from 'svelte';
  
  export let folder;
  export let isSelected = false;
  export let level = 0;

  const dispatch = createEventDispatcher();

  let isExpanded = false;

  function handleClick() {
    dispatch('select');
  }

  function toggleExpanded(event) {
    event.stopPropagation();
    isExpanded = !isExpanded;
  }

  function handleChildSelect(event) {
    dispatch('select', event.detail);
  }
</script>

<div class="folder-item" class:selected={isSelected} style="margin-left: {level * 1}rem">
  <div class="folder-content" on:click={handleClick}>
    {#if folder.children && folder.children.length > 0}
      <button class="expand-btn" on:click={toggleExpanded}>
        {isExpanded ? '▼' : '▶'}
      </button>
    {:else}
      <span class="expand-placeholder"></span>
    {/if}

    <div class="folder-info">
      {#if folder.cover_thumbnail}
        <img 
          src={folder.cover_thumbnail} 
          alt="文件夹封面" 
          class="folder-cover"
          loading="lazy"
        />
      {:else}
        <div class="folder-icon">📁</div>
      {/if}
      
      <div class="folder-details">
        <div class="folder-name" title={folder.name}>
          {folder.name}
        </div>
        <div class="folder-count">
          {folder.photo_count} 张图片
        </div>
      </div>
    </div>
  </div>

  {#if isExpanded && folder.children}
    <div class="children">
      {#each folder.children as child}
        <svelte:self 
          folder={child} 
          level={level + 1}
          on:select={handleChildSelect}
        />
      {/each}
    </div>
  {/if}
</div>

<style>
  .folder-item {
    border-radius: 4px;
    overflow: hidden;
  }

  .folder-content {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    cursor: pointer;
    transition: background-color 0.2s;
    border-radius: 4px;
  }

  .folder-content:hover {
    background-color: #333;
  }

  .folder-item.selected .folder-content {
    background-color: #007acc;
  }

  .expand-btn {
    background: none;
    border: none;
    color: #888;
    cursor: pointer;
    padding: 0.25rem;
    margin-right: 0.5rem;
    font-size: 0.8rem;
    width: 20px;
    text-align: center;
  }

  .expand-btn:hover {
    color: #fff;
  }

  .expand-placeholder {
    width: 20px;
    margin-right: 0.5rem;
  }

  .folder-info {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
  }

  .folder-cover {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
    margin-right: 0.75rem;
  }

  .folder-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #444;
    border-radius: 4px;
    margin-right: 0.75rem;
    font-size: 1.5rem;
  }

  .folder-details {
    flex: 1;
    min-width: 0;
  }

  .folder-name {
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 0.25rem;
  }

  .folder-count {
    font-size: 0.8rem;
    color: #888;
  }

  .children {
    margin-left: 1rem;
    border-left: 1px solid #444;
    padding-left: 0.5rem;
  }
</style>
