<script>
  import { onMount } from 'svelte';
  import { folders, selectedFolder, isLoadingFolders, currentPhotos } from '../stores.js';
  import FolderItem from './FolderItem.svelte';

  async function loadFolders() {
    isLoadingFolders.set(true);
    try {
      const response = await fetch('/api/folders');
      const result = await response.json();
      
      if (result.success) {
        folders.set(result.data || []);
      } else {
        console.error('加载文件夹失败:', result.message);
      }
    } catch (error) {
      console.error('加载文件夹错误:', error);
    } finally {
      isLoadingFolders.set(false);
    }
  }

  async function loadPhotos(folderPath) {
    try {
      const response = await fetch(`/api/photos?folder=${encodeURIComponent(folderPath)}`);
      const result = await response.json();
      
      if (result.success) {
        currentPhotos.set(result.data || []);
      } else {
        console.error('加载图片失败:', result.message);
        currentPhotos.set([]);
      }
    } catch (error) {
      console.error('加载图片错误:', error);
      currentPhotos.set([]);
    }
  }

  function handleFolderSelect(folderPath) {
    selectedFolder.set(folderPath);
    loadPhotos(folderPath);
  }

  onMount(() => {
    loadFolders();
  });

  // 监听selectedFolder变化，自动加载图片
  $: if ($selectedFolder) {
    loadPhotos($selectedFolder);
  }
</script>

<div class="folder-list">
  {#if $isLoadingFolders}
    <div class="loading">
      <span class="spinner"></span>
      加载文件夹中...
    </div>
  {:else if $folders.length === 0}
    <div class="empty-state">
      <p>未找到图片文件夹</p>
      <p class="hint">请确保图片目录中包含支持的图片文件</p>
    </div>
  {:else}
    {#each $folders as folder}
      <FolderItem 
        {folder} 
        isSelected={$selectedFolder === folder.path}
        on:select={() => handleFolderSelect(folder.path)}
      />
    {/each}
  {/if}
</div>

<style>
  .folder-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: #888;
    flex-direction: column;
    gap: 0.5rem;
  }

  .empty-state {
    padding: 2rem;
    text-align: center;
    color: #888;
  }

  .empty-state p {
    margin-bottom: 0.5rem;
  }

  .hint {
    font-size: 0.8rem;
    color: #666;
  }
</style>
