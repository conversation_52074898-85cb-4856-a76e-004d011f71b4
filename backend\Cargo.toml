[package]
name = "picsss-backend"
version = "0.1.0"
edition = "2021"

[dependencies]
# Web 框架
axum = "0.7"
tower = "0.4"
tower-http = { version = "0.5", features = ["fs", "cors", "trace"] }

# 异步运行时
tokio = { version = "1.0", features = ["full"] }

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 数据库
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid"] }

# 图像处理
image = "0.24"

# 文件系统和路径处理
walkdir = "2.4"
uuid = { version = "1.0", features = ["v4", "serde"] }

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# 日志
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# Base64 编码
base64 = "0.21"

# 环境变量
dotenvy = "0.15"

# HTTP 客户端和类型
hyper = "1.0"
mime_guess = "2.0"

# 并发处理
futures = "0.3"
