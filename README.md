# 本地写真集浏览器

一个高性能的本地写真集浏览器，使用 Rust + SvelteKit 构建，支持局域网访问。

## 技术栈

- **后端**: Rust + Axum
- **前端**: SvelteKit
- **数据库**: SQLite
- **图像处理**: image-rs
- **部署**: Docker

## 功能特性

- 🚀 高性能文件扫描和缩略图生成
- 📁 多层嵌套文件夹支持
- 🖼️ 瀑布流布局展示图片
- 🔍 沉浸式大图查看器
- ⌨️ 键盘导航支持
- 🔄 实时重新扫描
- 🌐 局域网访问支持

## 快速开始

### 使用 Docker 部署（推荐）

```bash
# 1. 克隆项目
git clone <repository-url>
cd picsss

# 2. 准备图片目录
mkdir photos
# 将您的图片文件放入 photos 目录

# 3. 启动应用
chmod +x scripts/start.sh
./scripts/start.sh

# 4. 访问应用
# 浏览器打开 http://localhost:3000
```

### 手动部署

```bash
# 1. 复制环境配置
cp .env.example .env

# 2. 启动服务
docker-compose up --build

# 3. 访问应用
# 浏览器打开 http://localhost:3000
```

### 本地开发

```bash
# 后端开发
cd backend
cargo run

# 前端开发
cd frontend
npm install
npm run dev
```

## 项目结构

```
picsss/
├── backend/           # Rust 后端
├── frontend/          # SvelteKit 前端
├── docker/           # Docker 配置文件
├── docker-compose.yml
└── README.md
```

## 配置

通过环境变量配置：

- `PHOTOS_ROOT_PATH`: 图片根目录路径
- `SERVER_PORT`: 服务器端口 (默认: 3000)
- `DATABASE_PATH`: 数据库文件路径

## 使用说明

### 首次使用

1. **准备图片**: 将图片文件放入 `photos` 目录，支持多层嵌套文件夹
2. **启动应用**: 使用上述快速开始步骤启动应用
3. **等待扫描**: 首次启动会自动扫描图片并生成缩略图
4. **开始浏览**: 在左侧选择文件夹，右侧查看图片

### 功能特性

- **文件夹浏览**: 左侧树状结构显示所有包含图片的文件夹
- **瀑布流展示**: 右侧以瀑布流布局展示图片缩略图
- **沉浸式查看**: 点击图片进入全屏查看模式
- **键盘导航**: 使用方向键切换图片，ESC键退出
- **缩放拖动**: 鼠标滚轮缩放，拖动查看大图细节
- **智能排序**: 支持按文件名或日期排序
- **实时扫描**: 支持重新扫描新增图片

### 支持的图片格式

- JPEG (.jpg, .jpeg)
- PNG (.png)
- GIF (.gif)
- WebP (.webp)
- BMP (.bmp)
- TIFF (.tiff, .tif)

## API 文档

- `GET /api/folders` - 获取文件夹树结构
- `GET /api/photos?folder=<path>` - 获取指定文件夹的图片列表
- `GET /photo/<encoded_path>` - 获取原始图片文件
- `POST /api/rescan` - 触发重新扫描
- `GET /api/scan-status` - 获取扫描状态

## 故障排除

详细的故障排除指南请参考 [DEPLOYMENT.md](./DEPLOYMENT.md)

## 贡献

欢迎提交 Issue 和 Pull Request！

## 许可证

MIT License
