<script>
  import { onMount, onDestroy } from 'svelte';
  import { viewerPhoto, isViewerOpen, currentPhotos } from '../stores.js';

  let viewerContainer;
  let imageElement;
  let scale = 1;
  let translateX = 0;
  let translateY = 0;
  let isDragging = false;
  let dragStartX = 0;
  let dragStartY = 0;
  let dragStartTranslateX = 0;
  let dragStartTranslateY = 0;

  // 预加载的图片缓存
  let preloadedImages = new Map();

  $: currentIndex = $currentPhotos.findIndex(p => p.id === $viewerPhoto?.id);
  $: hasNext = currentIndex < $currentPhotos.length - 1;
  $: hasPrev = currentIndex > 0;

  function close() {
    isViewerOpen.set(false);
    resetTransform();
  }

  function resetTransform() {
    scale = 1;
    translateX = 0;
    translateY = 0;
  }

  function nextPhoto() {
    if (hasNext) {
      viewerPhoto.set($currentPhotos[currentIndex + 1]);
      resetTransform();
    }
  }

  function prevPhoto() {
    if (hasPrev) {
      viewerPhoto.set($currentPhotos[currentIndex - 1]);
      resetTransform();
    }
  }

  function handleWheel(event) {
    event.preventDefault();
    
    const delta = event.deltaY > 0 ? -0.1 : 0.1;
    const newScale = Math.max(0.1, Math.min(5, scale + delta));
    
    if (newScale !== scale) {
      const rect = imageElement.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      
      const mouseX = event.clientX - centerX;
      const mouseY = event.clientY - centerY;
      
      const scaleRatio = newScale / scale;
      
      translateX = translateX * scaleRatio - mouseX * (scaleRatio - 1);
      translateY = translateY * scaleRatio - mouseY * (scaleRatio - 1);
      
      scale = newScale;
    }
  }

  function handleMouseDown(event) {
    if (event.button === 0) { // 左键
      isDragging = true;
      dragStartX = event.clientX;
      dragStartY = event.clientY;
      dragStartTranslateX = translateX;
      dragStartTranslateY = translateY;
      
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }
  }

  function handleMouseMove(event) {
    if (isDragging) {
      const deltaX = event.clientX - dragStartX;
      const deltaY = event.clientY - dragStartY;
      
      translateX = dragStartTranslateX + deltaX;
      translateY = dragStartTranslateY + deltaY;
    }
  }

  function handleMouseUp() {
    isDragging = false;
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  }

  function handleDoubleClick() {
    if (scale === 1) {
      scale = 2;
    } else {
      resetTransform();
    }
  }

  function preloadImage(photo) {
    if (!photo || preloadedImages.has(photo.id)) return;
    
    const img = new Image();
    img.src = `/photo/${photo.original_path}`;
    img.onload = () => {
      preloadedImages.set(photo.id, img);
    };
  }

  function getPhotoUrl(photo) {
    return `/photo/${photo.original_path}`;
  }

  // 预加载相邻图片
  $: if ($viewerPhoto && $currentPhotos.length > 0) {
    const index = currentIndex;
    
    // 预加载当前图片
    preloadImage($viewerPhoto);
    
    // 预加载前一张
    if (index > 0) {
      preloadImage($currentPhotos[index - 1]);
    }
    
    // 预加载后一张
    if (index < $currentPhotos.length - 1) {
      preloadImage($currentPhotos[index + 1]);
    }
  }

  // 当图片变化时重置变换
  $: if ($viewerPhoto) {
    resetTransform();
  }

  onDestroy(() => {
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  });
</script>

{#if $isViewerOpen && $viewerPhoto}
  <div class="viewer-overlay" bind:this={viewerContainer} on:click={close}>
    <div class="viewer-content" on:click|stopPropagation>
      <!-- 关闭按钮 -->
      <button class="close-btn" on:click={close}>×</button>
      
      <!-- 导航按钮 -->
      {#if hasPrev}
        <button class="nav-btn prev-btn" on:click={prevPhoto}>‹</button>
      {/if}
      
      {#if hasNext}
        <button class="nav-btn next-btn" on:click={nextPhoto}>›</button>
      {/if}
      
      <!-- 图片容器 -->
      <div class="image-container">
        <img
          bind:this={imageElement}
          src={getPhotoUrl($viewerPhoto)}
          alt={$viewerPhoto.file_name}
          style="transform: translate({translateX}px, {translateY}px) scale({scale})"
          on:wheel={handleWheel}
          on:mousedown={handleMouseDown}
          on:dblclick={handleDoubleClick}
          draggable="false"
        />
      </div>
      
      <!-- 图片信息 -->
      <div class="image-info">
        <div class="image-name">{$viewerPhoto.file_name}</div>
        <div class="image-details">
          {$viewerPhoto.width} × {$viewerPhoto.height} | 
          {currentIndex + 1} / {$currentPhotos.length}
          {#if scale !== 1}
            | 缩放: {Math.round(scale * 100)}%
          {/if}
        </div>
      </div>
      
      <!-- 缩放控制 -->
      <div class="zoom-controls">
        <button on:click={() => scale = Math.max(0.1, scale - 0.2)}>-</button>
        <span>{Math.round(scale * 100)}%</span>
        <button on:click={() => scale = Math.min(5, scale + 0.2)}>+</button>
        <button on:click={resetTransform}>重置</button>
      </div>
    </div>
  </div>
{/if}

<style>
  .viewer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.95);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .viewer-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .close-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.7);
    border: none;
    color: white;
    font-size: 2rem;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    z-index: 1001;
    transition: background-color 0.2s;
  }

  .close-btn:hover {
    background: rgba(0, 0, 0, 0.9);
  }

  .nav-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.7);
    border: none;
    color: white;
    font-size: 3rem;
    width: 60px;
    height: 80px;
    cursor: pointer;
    z-index: 1001;
    transition: background-color 0.2s;
  }

  .nav-btn:hover {
    background: rgba(0, 0, 0, 0.9);
  }

  .prev-btn {
    left: 20px;
  }

  .next-btn {
    right: 20px;
  }

  .image-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
  }

  .image-container img {
    max-width: 90vw;
    max-height: 90vh;
    object-fit: contain;
    cursor: grab;
    transition: transform 0.1s ease-out;
  }

  .image-container img:active {
    cursor: grabbing;
  }

  .image-info {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 1rem;
    border-radius: 8px;
    z-index: 1001;
  }

  .image-name {
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
  }

  .image-details {
    font-size: 0.9rem;
    color: #ccc;
  }

  .zoom-controls {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 8px;
    padding: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    z-index: 1001;
  }

  .zoom-controls button {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    min-width: 30px;
  }

  .zoom-controls button:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  .zoom-controls span {
    color: white;
    font-size: 0.9rem;
    min-width: 50px;
    text-align: center;
  }
</style>
