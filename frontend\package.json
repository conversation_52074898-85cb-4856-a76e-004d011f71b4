{"name": "picsss-frontend", "version": "0.1.0", "private": true, "scripts": {"build": "vite build", "dev": "vite dev", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "devDependencies": {"@sveltejs/adapter-static": "^3.0.1", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^3.0.0", "svelte": "^4.2.7", "svelte-check": "^3.6.0", "typescript": "^5.0.0", "vite": "^5.0.3"}, "dependencies": {"macy": "^2.5.1"}, "type": "module"}