services:
  picsss:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      # 挂载图片目录 - 请根据实际情况修改路径
      - "./photos:/app/photos:ro"
      # 挂载数据库和缓存目录
      - "./data:/app/data"
    environment:
      - PHOTOS_ROOT_PATH=/app/photos
      - SERVER_PORT=3000
      - DATABASE_PATH=/app/data/database.db
      - CACHE_PATH=/app/data/.cache
      - RUST_LOG=info
    restart: unless-stopped
    networks:
      - picsss-network

networks:
  picsss-network:
    driver: bridge
