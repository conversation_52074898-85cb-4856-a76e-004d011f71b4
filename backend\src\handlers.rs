use crate::{
    error::{AppError, Result},
    models::{ApiResponse, FolderInfo, PhotoResponse},
    AppState,
};
use axum::{
    extract::{Path, Query, State},
    http::{header, StatusCode},
    response::{IntoResponse, Response},
    Json,
};
use serde::Deserialize;
use std::{collections::HashMap, path::PathBuf};
use tokio::fs;
use base64::{engine::general_purpose, Engine as _};

#[derive(Deserialize)]
pub struct PhotosQuery {
    folder: Option<String>,
}

pub async fn health_check() -> impl IntoResponse {
    Json(ApiResponse::success("服务运行正常"))
}

pub async fn get_folders(State(state): State<AppState>) -> Result<impl IntoResponse> {
    let folders = state.db.get_all_folders().await?;
    let mut folder_tree = build_folder_tree(folders, &state).await?;

    Ok(<PERSON>son(ApiResponse::success(folder_tree)))
}

pub async fn get_photos(
    State(state): State<AppState>,
    Query(params): Query<PhotosQuery>,
) -> Result<impl IntoResponse> {
    let folder_path = params.folder.unwrap_or_else(|| ".".to_string());
    
    let photos = state.db.get_photos_by_folder(&folder_path).await?;
    let photo_responses: Vec<PhotoResponse> = photos
        .into_iter()
        .map(PhotoResponse::from)
        .collect();

    Ok(Json(ApiResponse::success(photo_responses)))
}

pub async fn get_photo_file(
    State(state): State<AppState>,
    Path(encoded_path): Path<String>,
) -> Result<impl IntoResponse> {
    // 解码Base64路径
    let decoded_bytes = base64::engine::general_purpose::STANDARD.decode(&encoded_path)?;
    let file_path_str = String::from_utf8(decoded_bytes)
        .map_err(|_| AppError::InvalidFileFormat)?;
    
    let file_path = PathBuf::from(&file_path_str);
    
    // 安全检查：确保文件在允许的根目录下
    let canonical_file_path = file_path.canonicalize()
        .map_err(|_| AppError::PathNotFound(file_path_str.clone()))?;
    
    let canonical_root_path = state.config.photos_root_path.canonicalize()
        .map_err(|_| AppError::Internal("根目录路径无效".to_string()))?;
    
    if !canonical_file_path.starts_with(&canonical_root_path) {
        return Err(AppError::PathNotFound("文件不在允许的目录中".to_string()));
    }

    // 检查文件是否存在
    if !canonical_file_path.exists() {
        return Err(AppError::PathNotFound(file_path_str));
    }

    // 读取文件
    let file_content = fs::read(&canonical_file_path).await?;
    
    // 确定MIME类型
    let mime_type = mime_guess::from_path(&canonical_file_path)
        .first_or_octet_stream()
        .to_string();

    // 返回文件响应
    Ok(Response::builder()
        .status(StatusCode::OK)
        .header(header::CONTENT_TYPE, mime_type)
        .header(header::CACHE_CONTROL, "public, max-age=31536000") // 缓存1年
        .body(file_content.into())
        .unwrap())
}

pub async fn rescan_photos(State(state): State<AppState>) -> Result<impl IntoResponse> {
    // 启动后台重新扫描任务
    let scanner = state.scanner.clone();
    tokio::spawn(async move {
        if let Err(e) = scanner.rescan().await {
            tracing::error!("重新扫描失败: {}", e);
        }
    });

    Ok(Json(ApiResponse::success("重新扫描已开始")))
}

pub async fn get_scan_status(State(state): State<AppState>) -> impl IntoResponse {
    let status = state.scanner.get_scan_status();
    Json(ApiResponse::success(status))
}

async fn build_folder_tree(
    folder_paths: Vec<String>,
    state: &AppState,
) -> Result<Vec<FolderInfo>> {
    let mut folder_map: HashMap<String, FolderInfo> = HashMap::new();
    let mut root_folders = Vec::new();

    // 创建所有文件夹节点
    for folder_path in &folder_paths {
        let path_buf = PathBuf::from(folder_path);
        let folder_name = path_buf
            .file_name()
            .unwrap_or_default()
            .to_string_lossy()
            .to_string();

        let mut folder_info = FolderInfo::new(folder_path.clone(), folder_name);
        
        // 获取封面图片
        folder_info.cover_thumbnail = state.db.get_folder_cover(folder_path).await?;
        
        // 获取图片数量
        folder_info.photo_count = state.db.get_folder_photo_count(folder_path).await?;

        folder_map.insert(folder_path.clone(), folder_info);
    }

    // 构建树状结构
    for folder_path in &folder_paths {
        let path_buf = PathBuf::from(folder_path);
        
        if let Some(parent_path) = path_buf.parent() {
            let parent_path_str = parent_path.to_string_lossy().to_string();
            
            // 如果父目录也在列表中，将当前目录作为子目录
            if folder_map.contains_key(&parent_path_str) {
                if let Some(folder_info) = folder_map.remove(folder_path) {
                    if let Some(parent_folder) = folder_map.get_mut(&parent_path_str) {
                        parent_folder.children.push(folder_info);
                    }
                }
            } else {
                // 父目录不在列表中，这是一个根目录
                if let Some(folder_info) = folder_map.remove(folder_path) {
                    root_folders.push(folder_info);
                }
            }
        } else {
            // 没有父目录，这是一个根目录
            if let Some(folder_info) = folder_map.remove(folder_path) {
                root_folders.push(folder_info);
            }
        }
    }

    // 添加剩余的文件夹（可能是由于路径处理问题）
    for (_, folder_info) in folder_map {
        root_folders.push(folder_info);
    }

    // 按名称排序
    root_folders.sort_by(|a, b| a.name.cmp(&b.name));
    for folder in &mut root_folders {
        sort_folder_children(folder);
    }

    Ok(root_folders)
}

fn sort_folder_children(folder: &mut FolderInfo) {
    folder.children.sort_by(|a, b| a.name.cmp(&b.name));
    for child in &mut folder.children {
        sort_folder_children(child);
    }
}
