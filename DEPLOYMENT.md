# 部署指南

## 快速开始

### 1. 准备环境

确保您的系统已安装：
- Docker
- Docker Compose

### 2. 克隆项目

```bash
git clone <repository-url>
cd picsss
```

### 3. 准备图片目录

```bash
# 创建图片目录
mkdir photos

# 将您的图片文件复制到 photos 目录中
# 支持多层嵌套文件夹结构
# 例如：
# photos/
# ├── 2023年旅行/
# │   ├── 春季/
# │   └── 夏季/
# └── 家庭照片/
#     ├── 生日聚会/
#     └── 节日庆典/
```

### 4. 启动应用

```bash
# 使用启动脚本（推荐）
chmod +x scripts/start.sh
./scripts/start.sh

# 或者直接使用 docker-compose
docker-compose up --build
```

### 5. 访问应用

打开浏览器访问：http://localhost:3000

## 配置选项

### 环境变量

复制 `.env.example` 为 `.env` 并根据需要修改：

```bash
cp .env.example .env
```

主要配置项：

- `PHOTOS_ROOT_PATH`: 图片根目录路径（默认：./photos）
- `SERVER_PORT`: 服务器端口（默认：3000）
- `DATABASE_PATH`: 数据库文件路径（默认：./data/database.db）
- `CACHE_PATH`: 缓存目录路径（默认：./data/.cache）

### 自定义图片目录

修改 `docker-compose.yml` 中的卷挂载：

```yaml
volumes:
  - "/path/to/your/photos:/app/photos:ro"  # 修改为您的图片目录
  - "./data:/app/data"
```

## 开发环境

### 后端开发

```bash
cd backend
cargo run
```

### 前端开发

```bash
cd frontend
npm install
npm run dev
```

### 使用开发环境 Docker

```bash
docker-compose -f docker-compose.dev.yml up
```

## 故障排除

### 常见问题

1. **端口被占用**
   - 修改 `docker-compose.yml` 中的端口映射
   - 或停止占用端口的其他服务

2. **图片无法显示**
   - 检查图片目录权限
   - 确保图片格式受支持（JPG, PNG, GIF, WebP, BMP, TIFF）

3. **扫描速度慢**
   - 大量图片首次扫描需要时间生成缩略图
   - 可以查看扫描进度（右上角状态显示）

4. **内存使用过高**
   - 调整 Docker 容器内存限制
   - 考虑分批处理大量图片

### 日志查看

```bash
# 查看应用日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f picsss
```

### 数据备份

重要数据位于 `./data` 目录：
- `database.db`: 图片索引数据库
- `.cache/thumbnails/`: 缩略图缓存

建议定期备份此目录。

## 性能优化

### 大量图片处理

对于包含大量图片的目录：

1. **分批扫描**: 首次部署时可以分批添加图片
2. **SSD存储**: 使用SSD存储可显著提升扫描速度
3. **内存配置**: 增加Docker容器内存限制

### 网络优化

1. **缓存设置**: 图片文件已配置长期缓存
2. **压缩**: 缩略图使用WebP格式，体积更小
3. **预加载**: 查看器会预加载相邻图片

## 安全注意事项

1. **文件访问**: 应用只能访问配置的图片目录
2. **网络访问**: 默认监听所有网络接口，请确保网络安全
3. **数据保护**: 建议定期备份数据库和缓存

## 更新应用

```bash
# 停止应用
docker-compose down

# 拉取最新代码
git pull

# 重新构建并启动
docker-compose up --build
```
