<script>
  import { onMount, afterUpdate } from 'svelte';
  import { sortedPhotos, sortBy, sortOrder, isLoadingPhotos, viewerPhoto, isViewerOpen } from '../stores.js';
  import Macy from 'macy';

  let gridContainer;
  let macyInstance;

  function openViewer(photo) {
    viewerPhoto.set(photo);
    isViewerOpen.set(true);
  }

  function initMasonry() {
    if (gridContainer && $sortedPhotos.length > 0) {
      // 销毁现有实例
      if (macyInstance) {
        macyInstance.destroy();
      }

      // 创建新的瀑布流实例
      macyInstance = Macy({
        container: gridContainer,
        trueOrder: false,
        waitForImages: true,
        margin: 16,
        columns: 6,
        breakAt: {
          1200: 5,
          940: 4,
          720: 3,
          520: 2,
          400: 1
        }
      });
    }
  }

  function handleSortChange(event) {
    const value = event.target.value;
    if (value.includes('name')) {
      sortBy.set('name');
      sortOrder.set(value.includes('desc') ? 'desc' : 'asc');
    } else if (value.includes('date')) {
      sortBy.set('date');
      sortOrder.set(value.includes('desc') ? 'desc' : 'asc');
    }
  }

  onMount(() => {
    return () => {
      if (macyInstance) {
        macyInstance.destroy();
      }
    };
  });

  afterUpdate(() => {
    // 当图片数据更新时重新初始化瀑布流
    if ($sortedPhotos.length > 0) {
      setTimeout(initMasonry, 100);
    }
  });
</script>

<div class="photo-grid-container">
  <div class="grid-header">
    <div class="photo-count">
      {$sortedPhotos.length} 张图片
    </div>
    
    <div class="sort-controls">
      <label for="sort-select">排序:</label>
      <select id="sort-select" on:change={handleSortChange}>
        <option value="name-asc">文件名 (A-Z)</option>
        <option value="name-desc">文件名 (Z-A)</option>
        <option value="date-asc">日期 (旧到新)</option>
        <option value="date-desc">日期 (新到旧)</option>
      </select>
    </div>
  </div>

  {#if $isLoadingPhotos}
    <div class="loading">
      <span class="spinner"></span>
      加载图片中...
    </div>
  {:else if $sortedPhotos.length === 0}
    <div class="empty-state">
      <p>此文件夹中没有图片</p>
      <p class="hint">支持的格式: JPG, PNG, GIF, WebP, BMP, TIFF</p>
    </div>
  {:else}
    <div class="photo-grid" bind:this={gridContainer}>
      {#each $sortedPhotos as photo (photo.id)}
        <div class="photo-item">
          <img
            src={photo.thumbnail_path}
            alt={photo.file_name}
            loading="lazy"
            on:click={() => openViewer(photo)}
            on:error={(e) => {
              e.target.style.display = 'none';
            }}
          />
          <div class="photo-overlay">
            <div class="photo-name">{photo.file_name}</div>
            <div class="photo-dimensions">{photo.width} × {photo.height}</div>
          </div>
        </div>
      {/each}
    </div>
  {/if}
</div>

<style>
  .photo-grid-container {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .grid-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0 0.5rem;
  }

  .photo-count {
    font-weight: 500;
    color: #ccc;
  }

  .sort-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .sort-controls label {
    color: #ccc;
    font-size: 0.9rem;
  }

  .sort-controls select {
    background-color: #333;
    color: #fff;
    border: 1px solid #555;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
    font-size: 0.9rem;
  }

  .photo-grid {
    flex: 1;
  }

  .photo-item {
    position: relative;
    cursor: pointer;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
  }

  .photo-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  }

  .photo-item img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.2s;
  }

  .photo-item:hover img {
    transform: scale(1.02);
  }

  .photo-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 1rem 0.75rem 0.75rem;
    opacity: 0;
    transition: opacity 0.2s;
  }

  .photo-item:hover .photo-overlay {
    opacity: 1;
  }

  .photo-name {
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .photo-dimensions {
    font-size: 0.8rem;
    color: #ccc;
  }

  .loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4rem;
    color: #888;
    flex-direction: column;
    gap: 1rem;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem;
    text-align: center;
    color: #888;
  }

  .empty-state p {
    margin-bottom: 0.5rem;
  }

  .hint {
    font-size: 0.8rem;
    color: #666;
  }
</style>
