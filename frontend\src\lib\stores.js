import { writable, derived } from 'svelte/store';

// 文件夹相关状态
export const folders = writable([]);
export const selectedFolder = writable(null);

// 图片相关状态
export const currentPhotos = writable([]);
export const sortBy = writable('name'); // 'name' | 'date'
export const sortOrder = writable('asc'); // 'asc' | 'desc'

// 查看器相关状态
export const isViewerOpen = writable(false);
export const viewerPhoto = writable(null);

// 加载状态
export const isLoadingFolders = writable(false);
export const isLoadingPhotos = writable(false);

// 扫描状态
export const scanStatus = writable({
  is_scanning: false,
  total_photos: 0,
  processed_photos: 0,
  current_folder: null
});

// 排序后的图片列表
export const sortedPhotos = derived(
  [currentPhotos, sortBy, sortOrder],
  ([$currentPhotos, $sortBy, $sortOrder]) => {
    const sorted = [...$currentPhotos];
    
    sorted.sort((a, b) => {
      let comparison = 0;
      
      if ($sortBy === 'name') {
        comparison = a.file_name.localeCompare(b.file_name);
      } else if ($sortBy === 'date') {
        // 假设我们有创建时间字段
        comparison = new Date(a.created_at || 0) - new Date(b.created_at || 0);
      }
      
      return $sortOrder === 'asc' ? comparison : -comparison;
    });
    
    return sorted;
  }
);

// 监听selectedFolder变化，保存到localStorage
selectedFolder.subscribe(value => {
  if (typeof window !== 'undefined' && value) {
    localStorage.setItem('lastSelectedFolder', value);
  }
});
